import { MCPTool, logger } from "mcp-framework";
import * as ynab from "ynab";
import { z } from "zod";

interface TransactionUpdateInput {
  accountId?: string;
  date?: string;
  amount?: number;
  payeeName?: string | null;
  payeeId?: string | null;
  categoryId?: string | null;
  memo?: string | null;
  cleared?: "cleared" | "uncleared" | "reconciled";
  approved?: boolean;
  flagColor?: string | null;
}

interface UpdateSingleTransactionInput {
  budgetId: string;
  transactionId: string;
  transaction: TransactionUpdateInput;
}

class UpdateSingleTransactionTool extends MCPTool<UpdateSingleTransactionInput> {
  name = "update_single_transaction";
  description = "Update a single existing transaction";

  schema = {
    budgetId: {
      type: z.string(),
      description: "The ID of the budget to update the transaction in",
    },
    transactionId: {
      type: z.string(),
      description: "The ID of the transaction to update",
    },
    transaction: {
      type: z.object({
        accountId: z.string().optional(),
        date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
        amount: z.number().optional().describe("The amount in dollars. Use negative values for expenses (e.g. -10.99) and positive values for income (e.g. 1000.00)"),
        payeeName: z.string().nullable().optional(),
        payeeId: z.string().nullable().optional(),
        categoryId: z.string().nullable().optional(),
        memo: z.string().nullable().optional(),
        cleared: z.enum(["cleared", "uncleared", "reconciled"]).optional(),
        approved: z.boolean().optional(),
        flagColor: z.string().nullable().optional(),
      }),
      description: "Transaction update object with optional fields to update",
    },
  };

  private api: ynab.API;

  constructor() {
    super();
    this.api = new ynab.API(process.env.YNAB_API_TOKEN || "");
  }

  async execute(input: UpdateSingleTransactionInput) {
    if (!process.env.YNAB_API_TOKEN) {
      return "YNAB API Token is not set";
    }

    if (!input.budgetId) {
      return "Budget ID is required. Please provide a budget ID.";
    }

    if (!input.transactionId) {
      return "Transaction ID is required. Please provide a transaction ID.";
    }

    if (!input.transaction || Object.keys(input.transaction).length === 0) {
      return "Transaction update object is required. Please provide at least one field to update.";
    }

    try {
      logger.info(`Updating transaction ${input.transactionId} for budget ${input.budgetId}`);
      
      // Transform the input transaction to match YNAB API format
      const transformedTransaction: ynab.ExistingTransaction = {};
      
      // Set updateable fields (only if provided)
      if (input.transaction.accountId) transformedTransaction.account_id = input.transaction.accountId;
      if (input.transaction.date) transformedTransaction.date = input.transaction.date;
      if (input.transaction.amount !== undefined) transformedTransaction.amount = Math.round(input.transaction.amount * 1000); // Convert to milliunits
      if (input.transaction.payeeName !== undefined) transformedTransaction.payee_name = input.transaction.payeeName;
      if (input.transaction.payeeId !== undefined) transformedTransaction.payee_id = input.transaction.payeeId;
      if (input.transaction.categoryId !== undefined) transformedTransaction.category_id = input.transaction.categoryId;
      if (input.transaction.memo !== undefined) transformedTransaction.memo = input.transaction.memo;
      if (input.transaction.cleared) transformedTransaction.cleared = input.transaction.cleared;
      if (input.transaction.approved !== undefined) transformedTransaction.approved = input.transaction.approved;
      if (input.transaction.flagColor !== undefined) transformedTransaction.flag_color = input.transaction.flagColor as ynab.TransactionFlagColor;

      const data: ynab.PutTransactionWrapper = {
        transaction: transformedTransaction,
      };

      const response = await this.api.transactions.updateTransaction(input.budgetId, input.transactionId, data);
      
      logger.info(`Successfully updated transaction ${input.transactionId}`);
      
      // Return a simple string response instead of complex object
      const updatedTransaction = response.data.transaction;
      const summary = {
        id: updatedTransaction.id,
        date: updatedTransaction.date,
        amount: updatedTransaction.amount,
        payee_name: updatedTransaction.payee_name,
        category_name: updatedTransaction.category_name,
        memo: updatedTransaction.memo,
        cleared: updatedTransaction.cleared,
        approved: updatedTransaction.approved,
      };

      return `Successfully updated transaction ${summary.id}. Date: ${summary.date}, Amount: ${summary.amount}, Payee: ${summary.payee_name || 'None'}, Category: ${summary.category_name || 'None'}, Memo: ${summary.memo || 'None'}, Cleared: ${summary.cleared}, Approved: ${summary.approved}`;
    } catch (error: unknown) {
      logger.error(`Error updating transaction ${input.transactionId} for budget ${input.budgetId}:`);
      logger.error(JSON.stringify(error, null, 2));
      return `Error updating transaction ${input.transactionId} for budget ${input.budgetId}: ${JSON.stringify(error)}`;
    }
  }
}

export default UpdateSingleTransactionTool; 