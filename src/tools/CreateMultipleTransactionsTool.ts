import { MCPTool, logger } from "mcp-framework";
import * as ynab from "ynab";
import { z } from "zod";

interface TransactionInput {
  accountId: string;
  date: string;
  amount: number;
  payeeName?: string | null;
  payeeId?: string | null;
  categoryId?: string | null;
  memo?: string | null;
  cleared?: "cleared" | "uncleared" | "reconciled";
  approved?: boolean;
  flagColor?: string | null;
  importId?: string | null;
}

interface CreateMultipleTransactionsInput {
  budgetId: string;
  transactions: TransactionInput[];
}

class CreateMultipleTransactionsTool extends MCPTool<CreateMultipleTransactionsInput> {
  name = "create_multiple_transactions";
  description = "Create multiple transactions at once (bulk import)";

  schema = {
    budgetId: {
      type: z.string(),
      description: "The ID of the budget to create transactions in",
    },
    transactions: {
      type: z.array(z.object({
        accountId: z.string(),
        date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
        amount: z.number().describe("The amount in dollars. Use negative values for expenses (e.g. -10.99) and positive values for income (e.g. 1000.00)"),
        payeeName: z.string().nullable().optional(),
        payeeId: z.string().nullable().optional(),
        categoryId: z.string().nullable().optional(),
        memo: z.string().nullable().optional(),
        cleared: z.enum(["cleared", "uncleared", "reconciled"]).optional(),
        approved: z.boolean().optional(),
        flagColor: z.string().nullable().optional(),
        importId: z.string().nullable().optional(),
      })),
      description: "Array of transaction objects to create",
    },
  };

  private api: ynab.API;

  constructor() {
    super();
    this.api = new ynab.API(process.env.YNAB_API_TOKEN || "");
  }

  async execute(input: CreateMultipleTransactionsInput) {
    if (!process.env.YNAB_API_TOKEN) {
      return "YNAB API Token is not set";
    }

    if (!input.budgetId) {
      return "Budget ID is required. Please provide a budget ID.";
    }

    if (!input.transactions || input.transactions.length === 0) {
      return "Transactions array is required and must not be empty. Please provide at least one transaction.";
    }

    try {
      logger.info(`Creating ${input.transactions.length} transactions for budget ${input.budgetId}`);
      
      // Transform the input transactions to match YNAB API format
      const transformedTransactions: ynab.NewTransaction[] = input.transactions.map(tx => ({
        account_id: tx.accountId,
        date: tx.date,
        amount: Math.round(tx.amount * 1000), // Preserves sign: negative for expenses, positive for income
        payee_name: tx.payeeName || undefined,
        payee_id: tx.payeeId || undefined,
        category_id: tx.categoryId || undefined,
        memo: tx.memo || undefined,
        cleared: tx.cleared,
        approved: tx.approved,
        flag_color: tx.flagColor as ynab.TransactionFlagColor || undefined,
        import_id: tx.importId || undefined,
      }));

      const data: ynab.PostTransactionsWrapper = {
        transactions: transformedTransactions,
      };

      const response = await this.api.transactions.createTransaction(input.budgetId, data);
      
      logger.info(`Successfully created ${response.data.transaction_ids.length} transactions`);
      
      if (response.data.duplicate_import_ids && response.data.duplicate_import_ids.length > 0) {
        logger.info(`Found ${response.data.duplicate_import_ids.length} duplicate import IDs`);
      }

      // Return a simple string response instead of complex object
      const summary = {
        total_requested: input.transactions.length,
        total_created: response.data.transaction_ids.length,
        duplicates_found: response.data.duplicate_import_ids?.length || 0,
        transaction_ids: response.data.transaction_ids,
      };

      return `Successfully created ${summary.total_created} out of ${summary.total_requested} transactions. Transaction IDs: ${summary.transaction_ids.join(', ')}${summary.duplicates_found > 0 ? ` (${summary.duplicates_found} duplicates found)` : ''}`;
    } catch (error: unknown) {
      logger.error(`Error creating multiple transactions for budget ${input.budgetId}:`);
      logger.error(JSON.stringify(error, null, 2));
      return `Error creating multiple transactions for budget ${input.budgetId}: ${JSON.stringify(error)}`;
    }
  }
}

export default CreateMultipleTransactionsTool; 