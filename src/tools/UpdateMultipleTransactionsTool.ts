import { MCPTool, logger } from "mcp-framework";
import * as ynab from "ynab";
import { z } from "zod";

interface TransactionUpdateInput {
  id?: string | null;
  importId?: string | null;
  accountId?: string;
  date?: string;
  amount?: number;
  payeeName?: string | null;
  payeeId?: string | null;
  categoryId?: string | null;
  memo?: string | null;
  cleared?: "cleared" | "uncleared" | "reconciled";
  approved?: boolean;
  flagColor?: string | null;
}

interface UpdateMultipleTransactionsInput {
  budgetId: string;
  transactions: TransactionUpdateInput[];
}

class UpdateMultipleTransactionsTool extends MCPTool<UpdateMultipleTransactionsInput> {
  name = "update_multiple_transactions";
  description = "Update multiple existing transactions at once (bulk update)";

  schema = {
    budgetId: {
      type: z.string(),
      description: "The ID of the budget to update transactions in",
    },
    transactions: {
      type: z.array(z.object({
        id: z.string().nullable().optional(),
        importId: z.string().nullable().optional(),
        accountId: z.string().optional(),
        date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
        amount: z.number().optional().describe("The amount in dollars. Use negative values for expenses (e.g. -10.99) and positive values for income (e.g. 1000.00)"),
        payeeName: z.string().nullable().optional(),
        payeeId: z.string().nullable().optional(),
        categoryId: z.string().nullable().optional(),
        memo: z.string().nullable().optional(),
        cleared: z.enum(["cleared", "uncleared", "reconciled"]).optional(),
        approved: z.boolean().optional(),
        flagColor: z.string().nullable().optional(),
      }).refine((data) => {
        // Either id OR importId must be provided, but not both
        const hasId = data.id && data.id !== null;
        const hasImportId = data.importId && data.importId !== null;
        return (hasId && !hasImportId) || (!hasId && hasImportId);
      }, {
        message: "Either 'id' OR 'importId' must be provided, but not both",
      })),
      description: "Array of transaction update objects. Each transaction must have either 'id' or 'importId' for identification",
    },
  };

  private api: ynab.API;

  constructor() {
    super();
    this.api = new ynab.API(process.env.YNAB_API_TOKEN || "");
  }

  async execute(input: UpdateMultipleTransactionsInput) {
    if (!process.env.YNAB_API_TOKEN) {
      return "YNAB API Token is not set";
    }

    if (!input.budgetId) {
      return "Budget ID is required. Please provide a budget ID.";
    }

    if (!input.transactions || input.transactions.length === 0) {
      return "Transactions array is required and must not be empty. Please provide at least one transaction to update.";
    }

    try {
      logger.info(`Updating ${input.transactions.length} transactions for budget ${input.budgetId}`);
      
      // Transform the input transactions to match YNAB API format
      const transformedTransactions: ynab.SaveTransactionWithIdOrImportId[] = input.transactions.map(tx => {
        const transformed: ynab.SaveTransactionWithIdOrImportId = {};
        
        // Set identification (either id OR import_id, not both)
        if (tx.id) {
          transformed.id = tx.id;
        } else if (tx.importId) {
          transformed.import_id = tx.importId;
        }
        
        // Set updateable fields (only if provided)
        if (tx.accountId) transformed.account_id = tx.accountId;
        if (tx.date) transformed.date = tx.date;
        if (tx.amount !== undefined) transformed.amount = Math.round(tx.amount * 1000); // Convert to milliunits
        if (tx.payeeName !== undefined) transformed.payee_name = tx.payeeName;
        if (tx.payeeId !== undefined) transformed.payee_id = tx.payeeId;
        if (tx.categoryId !== undefined) transformed.category_id = tx.categoryId;
        if (tx.memo !== undefined) transformed.memo = tx.memo;
        if (tx.cleared) transformed.cleared = tx.cleared;
        if (tx.approved !== undefined) transformed.approved = tx.approved;
        if (tx.flagColor !== undefined) transformed.flag_color = tx.flagColor as ynab.TransactionFlagColor;
        
        return transformed;
      });

      const data: ynab.PatchTransactionsWrapper = {
        transactions: transformedTransactions,
      };

      const response = await this.api.transactions.updateTransactions(input.budgetId, data);
      
      logger.info(`Successfully updated ${response.data.transaction_ids.length} transactions`);
      
      if (response.data.duplicate_import_ids && response.data.duplicate_import_ids.length > 0) {
        logger.info(`Found ${response.data.duplicate_import_ids.length} duplicate import IDs`);
      }

      // Return a simple string response instead of complex object
      const summary = {
        total_requested: input.transactions.length,
        total_updated: response.data.transaction_ids.length,
        duplicates_found: response.data.duplicate_import_ids?.length || 0,
        transaction_ids: response.data.transaction_ids,
      };

      return `Successfully updated ${summary.total_updated} out of ${summary.total_requested} transactions. Transaction IDs: ${summary.transaction_ids.join(', ')}${summary.duplicates_found > 0 ? ` (${summary.duplicates_found} duplicates found)` : ''}`;
    } catch (error: unknown) {
      logger.error(`Error updating multiple transactions for budget ${input.budgetId}:`);
      logger.error(JSON.stringify(error, null, 2));
      return `Error updating multiple transactions for budget ${input.budgetId}: ${JSON.stringify(error)}`;
    }
  }
}

export default UpdateMultipleTransactionsTool; 