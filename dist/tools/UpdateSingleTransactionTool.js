import { MCPTool, logger } from "mcp-framework";
import * as ynab from "ynab";
import { z } from "zod";
class UpdateSingleTransactionTool extends MCPTool {
    name = "update_single_transaction";
    description = "Update a single existing transaction";
    schema = {
        budgetId: {
            type: z.string(),
            description: "The ID of the budget to update the transaction in",
        },
        transactionId: {
            type: z.string(),
            description: "The ID of the transaction to update",
        },
        accountId: {
            type: z.string().optional(),
            description: "The account ID to update the transaction to",
        },
        date: {
            type: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
            description: "The transaction date in YYYY-MM-DD format",
        },
        amount: {
            type: z.number().optional(),
            description: "The amount in dollars. Use negative values for expenses (e.g. -10.99) and positive values for income (e.g. 1000.00)",
        },
        payeeName: {
            type: z.string().nullable().optional(),
            description: "The payee name to update",
        },
        payeeId: {
            type: z.string().nullable().optional(),
            description: "The payee ID to update",
        },
        categoryId: {
            type: z.string().nullable().optional(),
            description: "The category ID to update",
        },
        memo: {
            type: z.string().nullable().optional(),
            description: "The memo to update",
        },
        cleared: {
            type: z.enum(["cleared", "uncleared", "reconciled"]).optional(),
            description: "The cleared status to update",
        },
        approved: {
            type: z.boolean().optional(),
            description: "The approved status to update",
        },
        flagColor: {
            type: z.string().nullable().optional(),
            description: "The flag color to update",
        },
    };
    api;
    constructor() {
        super();
        this.api = new ynab.API(process.env.YNAB_API_TOKEN || "");
    }
    async execute(input) {
        if (!process.env.YNAB_API_TOKEN) {
            return "YNAB API Token is not set";
        }
        if (!input.budgetId) {
            return "Budget ID is required. Please provide a budget ID.";
        }
        if (!input.transactionId) {
            return "Transaction ID is required. Please provide a transaction ID.";
        }
        // Check if at least one update field is provided
        const hasUpdateFields = input.accountId || input.date || input.amount !== undefined ||
            input.payeeName !== undefined || input.payeeId !== undefined ||
            input.categoryId !== undefined || input.memo !== undefined ||
            input.cleared || input.approved !== undefined || input.flagColor !== undefined;
        if (!hasUpdateFields) {
            return "At least one field to update is required. Please provide at least one field to update.";
        }
        try {
            logger.info(`Updating transaction ${input.transactionId} for budget ${input.budgetId}`);
            // Transform the input transaction to match YNAB API format
            const transformedTransaction = {};
            // Set updateable fields (only if provided)
            if (input.accountId)
                transformedTransaction.account_id = input.accountId;
            if (input.date)
                transformedTransaction.date = input.date;
            if (input.amount !== undefined)
                transformedTransaction.amount = Math.round(input.amount * 1000); // Convert to milliunits
            if (input.payeeName !== undefined)
                transformedTransaction.payee_name = input.payeeName;
            if (input.payeeId !== undefined)
                transformedTransaction.payee_id = input.payeeId;
            if (input.categoryId !== undefined)
                transformedTransaction.category_id = input.categoryId;
            if (input.memo !== undefined)
                transformedTransaction.memo = input.memo;
            if (input.cleared)
                transformedTransaction.cleared = input.cleared;
            if (input.approved !== undefined)
                transformedTransaction.approved = input.approved;
            if (input.flagColor !== undefined)
                transformedTransaction.flag_color = input.flagColor;
            const data = {
                transaction: transformedTransaction,
            };
            const response = await this.api.transactions.updateTransaction(input.budgetId, input.transactionId, data);
            logger.info(`Successfully updated transaction ${input.transactionId}`);
            // Return a simple string response instead of complex object
            const updatedTransaction = response.data.transaction;
            const summary = {
                id: updatedTransaction.id,
                date: updatedTransaction.date,
                amount: updatedTransaction.amount,
                payee_name: updatedTransaction.payee_name,
                category_name: updatedTransaction.category_name,
                memo: updatedTransaction.memo,
                cleared: updatedTransaction.cleared,
                approved: updatedTransaction.approved,
            };
            return `Successfully updated transaction ${summary.id}. Date: ${summary.date}, Amount: ${summary.amount}, Payee: ${summary.payee_name || 'None'}, Category: ${summary.category_name || 'None'}, Memo: ${summary.memo || 'None'}, Cleared: ${summary.cleared}, Approved: ${summary.approved}`;
        }
        catch (error) {
            logger.error(`Error updating transaction ${input.transactionId} for budget ${input.budgetId}:`);
            logger.error(JSON.stringify(error, null, 2));
            return `Error updating transaction ${input.transactionId} for budget ${input.budgetId}: ${JSON.stringify(error)}`;
        }
    }
}
export default UpdateSingleTransactionTool;
