YNAB MCP Server - Product Backlog
This document outlines the epics and user stories for extending the ynab-mcp-server to cover the entire YNAB API.

## Lessons Learned (Updated 2024-12-19)
- **Authentication is already proven** by existing working tools - no need for separate user verification
- **BudgetSummaryTool already provides** accounts and categories data via `api.accounts.getAccounts()` and `api.months.getBudgetMonth()`
- **Avoid over-engineering** - existing tools show the pattern, new tools should follow the same simple approach
- **Focus on new value** - only create tools that provide data not already available
- **Test against live YNAB development budget** for all new tools

Epic 1: Payee Data Read-Access ✅ COMPLETE
Goal: Establish tools to read payee data from the YNAB budget (accounts and categories already available via BudgetSummaryTool).

Stories:

[x] Done - ListBudgetsTool.ts

[x] Done - GetUnapprovedTransactionsTool.ts

[x] Done - BudgetSummaryTool.ts (provides accounts and categories data)

[x] Done - CreateTransactionTool.ts

[x] Done - ApproveTransactionTool.ts

[Won't Do] Story 1.1: Get authenticated user details. (Authentication already proven by existing tools)

[Won't Do] Story 1.2: Get a list of all accounts for a budget. (Already available via BudgetSummaryTool)

[Won't Do] Story 1.3: Get the details of a single account. (Data available in BudgetSummaryTool accounts array)

[Won't Do] Story 1.4: Get a list of all categories for a budget. (Already available via BudgetSummaryTool)

[Won't Do] Story 1.5: Get the details of a single category. (Data available in BudgetSummaryTool categories array)

[x] Done - Story 1.6: Get a list of all payees for a budget.

[x] Done - Story 1.7: Get the details of a single payee.

[Won't Do] Story 1.8: Get a list of all payee locations for a budget. (Low value - location data not commonly used, limited adoption)

[Won't Do] Story 1.9: Get the details of a single payee location. (Low value - location data not commonly used, limited adoption)

Epic 2: Transaction Management
Goal: Enable full transaction management, building upon the tools we already have.

Stories:

[ ] Story 2.1: Create multiple transactions at once (bulk import).

[ ] Story 2.2: Update multiple transactions at once (bulk update).

[ ] Story 2.3: Update a single existing transaction.

[ ] Story 2.4: Delete a single existing transaction.

[ ] Story 2.5: Get a list of all scheduled transactions for a budget.

[ ] Story 2.6: Get the details of a single scheduled transaction.

[ ] Story 2.7: Create a new scheduled transaction.

[ ] Story 2.8: Update an existing scheduled transaction.

[ ] Story 2.9: Delete an existing scheduled transaction.

Epic 3: Budget & Month Management
Goal: Provide tools to interact with the budget on a monthly basis and manage category-specific data.

Stories:

[ ] Story 3.1: Update a category's budgeted amount for a specific month.

[ ] Story 3.2: Get the details for a single budget month.

[ ] Story 3.3: Get a list of all budget months.

This document provides the complete roadmap for our project. The next step is for the Scrum Master to create the story file for Story 1.6: Get a list of all payees for a budget.

