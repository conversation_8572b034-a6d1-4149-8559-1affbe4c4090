---
defaultOutput: docs/stories/1.6.GetPayeesList.story.md
smAgent:
  editableSections: Status, Story, Acceptance Criteria, Tasks / Subtasks, Dev Notes, Testing, Change Log
  sectionSpecificInstructions:
    "Dev Notes":
      - Populate relevant information, only what was pulled from actual artifacts from docs folder, relevant to this story
      - Do not invent information.
      - If known add Relevant Source Tree info that relates to this story.
      - If there were important notes from previous story that are relevant to this one, include them here.
      - Put enough information in this section so that the dev agent should NEVER need to read the architecture documents, these notes along with the tasks and subtasks must give the Dev Agent the complete context it needs to comprehend with the least amount of overhead the information to complete the story,  meeting all AC and completing all tasks+subtasks.
    Testing:
      - List Relevant Testing Standards from Architecture the Developer needs to conform to (test file location, test standards, etc) 
---

# Story 1.6: Get a list of all payees for a budget

## Status: Ready for Review

## Story

**As a** user of the YNAB MCP server,\
**I want** to retrieve a list of all payees in my budget,\
**so that** I can see available payees for transaction creation and management

## Acceptance Criteria

1. <PERSON>l successfully retrieves all payees from YNAB API for a specified budget
2. Tool returns payee data in a clear, structured format (id, name, transfer_account_id, deleted status)
3. Tool handles authentication errors gracefully with meaningful error messages
4. Tool follows the same patterns as existing tools (ListBudgetsTool, BudgetSummaryTool, etc.)
5. Tool includes proper logging for debugging and monitoring
6. Tool is properly integrated into the MCP server and available for use
7. Tool is tested against live YNAB development budget

## Tasks / Subtasks

- [x] Task 1: Create GetPayeesTool.ts file (AC: 1, 2, 4, 6)
  - [x] Create new file `src/tools/GetPayeesTool.ts`
  - [x] Import required dependencies (MCPTool, logger, ynab API)
  - [x] Define tool schema with budgetId parameter (optional, like BudgetSummaryTool)
  - [x] Implement execute method to call YNAB payees API
  - [x] Add proper error handling and logging
  - [x] Return payee data in consistent format
- [x] Task 2: Register tool in MCP server (AC: 6)
  - [x] Import GetPayeesTool in main server file
  - [x] Add tool to server registration
  - [x] Verify tool is available in MCP server
- [x] Task 3: Create unit tests (AC: 3, 5)
  - [x] Create test file `src/tools/__tests__/GetPayeesTool.test.ts`
  - [x] Test successful payees retrieval
  - [x] Test error handling scenarios
  - [x] Test logging functionality
- [x] Task 4: Integration testing with live YNAB API (AC: 1, 2, 3, 7)
  - [x] Test tool against live YNAB development budget
  - [x] Verify payee data structure matches expectations
  - [x] Test error scenarios with invalid tokens
  - [x] Document test results

## Dev Notes

### Previous Story Insights
- Existing tools follow consistent pattern: extend MCPTool, use ynab API, include proper error handling
- All tools use empty schema object or simple schema with optional parameters
- Tools are registered in the main server file
- Error handling includes try-catch with logger.error and return error message
- Successful responses are logged with logger.info
- **Lesson learned**: Keep it simple, follow existing patterns, don't over-engineer

### Data Models
- **Payees API Response Structure** [Source: node_modules/ynab/dist/models/PayeesResponse.d.ts]:
  ```typescript
  interface PayeesResponse {
    data: PayeesResponseData;
  }
  
  interface PayeesResponseData {
    payees: Array<Payee>;
    server_knowledge: number;
  }
  
  interface Payee {
    id: string;
    name: string;
    transfer_account_id?: string | null;
    deleted: boolean;
  }
  ```

### API Specifications
- **YNAB Payees API Endpoint** [Source: node_modules/ynab/dist/apis/PayeesApi.d.ts]:
  - Method: `api.payees.getPayees(budgetId, lastKnowledgeOfServer?)`
  - Returns: `Promise<PayeesResponse>`
  - Parameters: budgetId (required), lastKnowledgeOfServer (optional)
  - Uses existing authentication token from environment

### File Locations
- **New Tool File**: `src/tools/GetPayeesTool.ts`
- **Test File**: `src/tools/__tests__/GetPayeesTool.test.ts`
- **Server Registration**: `src/index.ts` (if needed for tool registration)
- **Follows existing pattern**: Same structure as `src/tools/ListBudgetsTool.ts`

### Technical Constraints
- **Authentication**: Uses `process.env.YNAB_API_TOKEN` (same as existing tools)
- **Budget ID**: Uses `process.env.YNAB_BUDGET_ID` or accepts as parameter (like BudgetSummaryTool)
- **Error Handling**: Must handle missing API token and API errors gracefully
- **Logging**: Use `logger.info` for success, `logger.error` for failures
- **Return Format**: Return payees array, or error message string
- **Dependencies**: Uses existing `ynab` npm package and `mcp-framework`

### Testing Requirements
- **Unit Tests**: Use Vitest framework (same as existing tests)
- **Test Location**: `src/tools/__tests__/GetPayeesTool.test.ts`
- **Live Testing**: Test against YNAB development budget using existing API token
- **Error Scenarios**: Test missing token, invalid token, network errors
- **Success Scenarios**: Verify payee data is returned correctly

### Relevant Source Tree Info
- **Existing Tools Pattern**: `src/tools/ListBudgetsTool.ts` shows the exact pattern to follow
- **MCP Framework**: Uses `MCPTool` base class and `logger` from `mcp-framework`
- **YNAB API**: Uses `ynab.API` constructor with token, then `api.payees.getPayees()`

### New Value Provided
- **Unique functionality**: No existing tools provide payee data
- **Transaction creation**: Enables payee selection for new transactions
- **Data completeness**: Provides full payee list for budget management

## Testing

### Testing Standards
- **Framework**: Vitest (as configured in package.json)
- **Test Location**: `src/tools/__tests__/GetPayeesTool.test.ts`
- **Coverage**: Unit tests for all code paths
- **Live Testing**: Must test against actual YNAB development budget
- **Error Testing**: Test all error scenarios (missing token, API errors)

### Key Test Scenarios
1. **Successful Payees Retrieval**: Verify payee list is returned correctly
2. **Missing API Token**: Should return meaningful error message
3. **Invalid API Token**: Should handle authentication errors gracefully
4. **Network Errors**: Should handle API connection issues
5. **Logging Verification**: Ensure proper logging on success and failure
6. **Live Budget Testing**: Test against YNAB development budget

## Change Log

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
| 2024-12-19 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used: Claude Sonnet 4 (James - Dev Agent)

### Debug Log References
- Build successful: TypeScript compilation and MCP build completed without errors
- Unit tests: All 6 test cases passed (error handling, success scenarios, env var handling)
- Live API testing: Confirmed by user that tool successfully retrieves payees from YNAB

### Completion Notes List
- Created GetPayeesTool.ts following established pattern from ListBudgetsTool.ts and BudgetSummaryTool.ts
- Implemented optional budgetId parameter with environment variable fallback
- Added comprehensive error handling for missing API token and budget ID
- Included proper logging for debugging and monitoring
- Created unit tests covering all code paths including success, error, and edge cases
- Tool auto-registers with MCP server (no manual registration needed)
- Live testing confirmed tool works with actual YNAB development budget

### File List
- Created: `src/tools/GetPayeesTool.ts`
- Created: `src/tools/__tests__/GetPayeesTool.test.ts`
- Modified: `docs/stories/1.6.story.md` (updated task completion and Dev Agent Record)

## QA Results 