---
defaultOutput: docs/stories/2.2.UpdateMultipleTransactions.story.md
smAgent:
  editableSections: Status, Story, Acceptance Criteria, Tasks / Subtasks, Dev Notes, Testing, Change Log
  sectionSpecificInstructions:
    "Dev Notes":
      - Populate relevant information, only what was pulled from actual artifacts from docs folder, relevant to this story
      - Do not invent information.
      - If known add Relevant Source Tree info that relates to this story.
      - If there were important notes from previous story that are relevant to this one, include them here.
      - Put enough information in this section so that the dev agent should NEVER need to read the architecture documents, these notes along with the tasks and subtasks must give the Dev Agent the complete context it needs to comprehend with the least amount of overhead the information to complete the story,  meeting all AC and completing all tasks+subtasks.
    Testing:
      - List Relevant Testing Standards from Architecture the Developer needs to conform to (test file location, test standards, etc) 
---

# Story 2.2: Update multiple transactions at once (bulk update)

## Status: Draft

## Story

**As a** user of the YNAB MCP server,\
**I want** to update multiple existing transactions in a single operation,\
**so that** I can efficiently modify bulk transactions and reduce the number of API calls needed

## Acceptance Criteria

1. Tool successfully updates multiple transactions from YNAB API for a specified budget
2. Tool accepts an array of transaction update objects with required fields (id or import_id) and optional update fields
3. Tool returns updated transaction data in a clear, structured format with transaction IDs
4. Tool handles authentication errors gracefully with meaningful error messages
5. Tool follows the same patterns as existing tools (CreateMultipleTransactionsTool, CreateTransactionTool, etc.)
6. Tool includes proper logging for debugging and monitoring
7. Tool is properly integrated into the MCP server and available for use
8. Tool is tested against live YNAB development budget
9. Tool validates required transaction fields and provides helpful error messages
10. Tool handles partial failures gracefully (some transactions succeed, others fail)
11. Tool supports updating transactions by either transaction ID or import ID
12. Tool allows updating any combination of transaction fields (amount, date, payee, category, memo, etc.)

## Tasks / Subtasks

- [ ] Task 1: Create UpdateMultipleTransactionsTool.ts file (AC: 1, 2, 4, 5, 6, 9, 11, 12)
  - [ ] Create new file `src/tools/UpdateMultipleTransactionsTool.ts`
  - [ ] Import required dependencies (MCPTool, logger, ynab API, zod)
  - [ ] Define tool schema with budgetId and transactions array parameters
  - [ ] Implement execute method to call YNAB transactions API with bulk update data
  - [ ] Add proper error handling and logging
  - [ ] Return updated transaction data in consistent format
  - [ ] Support both transaction ID and import ID identification
- [ ] Task 2: Register tool in MCP server (AC: 7)
  - [ ] Tool auto-registers with MCP server (no manual registration needed)
  - [ ] Verify tool is available in MCP server
- [ ] Task 3: Create unit tests (AC: 4, 6, 9, 10, 11, 12)
  - [ ] Create test file `src/tools/__tests__/UpdateMultipleTransactionsTool.test.ts`
  - [ ] Test successful bulk transaction updates
  - [ ] Test error handling scenarios (missing parameters, invalid data, partial failures)
  - [ ] Test logging functionality
  - [ ] Test both ID and import ID identification methods
- [ ] Task 4: Integration testing with live YNAB API (AC: 1, 2, 3, 8)
  - [ ] Test tool against live YNAB development budget
  - [ ] Verify transaction data structure matches expectations
  - [ ] Test error scenarios with invalid tokens and transaction data
  - [ ] Document test results

## Dev Notes

### Previous Story Insights
- Epic 1 completed successfully with GetPayeesTool and GetSinglePayeeTool
- Story 2.1 (CreateMultipleTransactionsTool) completed successfully - this builds on that pattern
- Existing tools follow consistent pattern: extend MCPTool, use ynab API, include proper error handling
- Error handling includes try-catch with logger.error and return error message
- Successful responses are logged with logger.info
- **Lesson learned**: Keep it simple, follow existing patterns, don't over-engineer
- **New insight**: Bulk update operations require transaction identification (ID or import_id) and partial field updates

### Data Models
- **Bulk Transaction Update Request** [Source: node_modules/ynab/dist/models/PatchTransactionsWrapper.d.ts]:
  ```typescript
  interface PatchTransactionsWrapper {
    transactions: Array<SaveTransactionWithIdOrImportId>;
  }
  ```

- **Transaction Update Structure** [Source: node_modules/ynab/dist/models/SaveTransactionWithIdOrImportId.d.ts]:
  ```typescript
  interface SaveTransactionWithIdOrImportId {
    // Identification (either id OR import_id, not both)
    id?: string | null;
    import_id?: string | null;
    
    // Updateable fields (all optional)
    account_id?: string;
    date?: string;  // YYYY-MM-DD format
    amount?: number;  // Amount in milliunits (cents * 10)
    payee_name?: string | null;
    payee_id?: string | null;
    category_id?: string | null;
    memo?: string | null;
    cleared?: "cleared" | "uncleared" | "reconciled";
    approved?: boolean;
    flag_color?: string | null;
    subtransactions?: Array<SaveSubTransaction>;
  }
  ```

- **Bulk Transaction Update Response** [Source: node_modules/ynab/dist/models/SaveTransactionsResponse.d.ts]:
  ```typescript
  interface SaveTransactionsResponse {
    data: SaveTransactionsResponseData;
  }
  
  interface SaveTransactionsResponseData {
    transaction_ids: Array<string>;
    duplicate_import_ids: Array<string>;
    transactions: Array<TransactionDetail>;
  }
  ```

### API Specifications
- **YNAB Bulk Transaction Update API Endpoint** [Source: node_modules/ynab/dist/apis/TransactionsApi.d.ts]:
  - Method: `api.transactions.updateTransactions(budgetId, data)`
  - Returns: `Promise<SaveTransactionsResponse>`
  - Parameters: budgetId (required), data (PatchTransactionsWrapper with transactions array)
  - Uses existing authentication token from environment
  - Supports updating transactions by either `id` or `import_id` (not both)

### File Locations
- **New Tool File**: `src/tools/UpdateMultipleTransactionsTool.ts`
- **Test File**: `src/tools/__tests__/UpdateMultipleTransactionsTool.test.ts`
- **Server Registration**: Auto-registration (no manual registration needed)
- **Follows existing pattern**: Same structure as `src/tools/CreateMultipleTransactionsTool.ts` but for bulk updates

### Technical Constraints
- **Authentication**: Uses `process.env.YNAB_API_TOKEN` (same as existing tools)
- **Budget ID**: Required parameter (no environment variable fallback for bulk operations)
- **Transaction Array**: Required parameter - must be provided by user
- **Transaction Identification**: Each transaction must have either `id` OR `import_id` (not both)
- **Amount Format**: Must be in milliunits (cents * 10) as per YNAB API
- **Date Format**: Must be YYYY-MM-DD format
- **Error Handling**: Must handle missing API token, missing parameters, invalid transaction data, and partial failures
- **Logging**: Use `logger.info` for success, `logger.error` for failures
- **Return Format**: Return updated transaction data with IDs, or error message string
- **Dependencies**: Uses existing `ynab` npm package and `mcp-framework`

### Testing Requirements
- **Unit Tests**: Use Vitest framework (same as existing tests)
- **Test Location**: `src/tools/__tests__/UpdateMultipleTransactionsTool.test.ts`
- **Live Testing**: Test against YNAB development budget using existing API token
- **Error Scenarios**: Test missing token, missing parameters, invalid transaction data, partial failures
- **Success Scenarios**: Verify bulk transaction updates and returned transaction IDs
- **Identification Testing**: Test both ID and import ID identification methods

### Relevant Source Tree Info
- **Existing Tools Pattern**: `src/tools/CreateMultipleTransactionsTool.ts` shows the exact pattern to follow
- **MCP Framework**: Uses `MCPTool` base class and `logger` from `mcp-framework`
- **YNAB API**: Uses `ynab.API` constructor with token, then `api.transactions.updateTransactions()`
- **Parameter Validation**: Use Zod schema with required parameters (budgetId, transactions array)
- **Key Difference**: Uses `PatchTransactionsWrapper` instead of `PostTransactionsWrapper`

### New Value Provided
- **Bulk transaction updates**: Updates multiple transactions in a single API call
- **Efficiency**: Reduces API calls for bulk modifications
- **Transaction management**: Enables bulk transaction update workflows
- **Error handling**: Provides partial failure handling for bulk operations
- **Data validation**: Ensures proper transaction identification and field validation
- **Flexibility**: Supports updating any combination of transaction fields

## Testing

### Testing Standards
- **Framework**: Vitest (as configured in package.json)
- **Test Location**: `src/tools/__tests__/UpdateMultipleTransactionsTool.test.ts`
- **Coverage**: Unit tests for all code paths
- **Live Testing**: Must test against actual YNAB development budget
- **Error Testing**: Test all error scenarios (missing token, missing parameters, invalid transaction data, partial failures)

### Key Test Scenarios
1. **Successful Bulk Transaction Updates**: Verify multiple transactions are updated correctly
2. **Missing API Token**: Should return meaningful error message
3. **Missing Budget ID**: Should return error message about required parameter
4. **Missing Transactions Array**: Should return error message about required parameter
5. **Invalid Transaction Data**: Should handle validation errors gracefully
6. **Partial Failures**: Should handle cases where some transactions succeed and others fail
7. **Network Errors**: Should handle API connection issues
8. **Logging Verification**: Ensure proper logging on success and failure
9. **Live Budget Testing**: Test against YNAB development budget with valid transaction data
10. **ID vs Import ID Testing**: Test both identification methods
11. **Field Update Testing**: Test updating various combinations of transaction fields

## Change Log

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
| 2024-12-19 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used: 

### Debug Log References

### Completion Notes List

### File List

## QA Results 