---
defaultOutput: docs/stories/2.1.CreateMultipleTransactions.story.md
smAgent:
  editableSections: Status, Story, Acceptance Criteria, Tasks / Subtasks, Dev Notes, Testing, Change Log
  sectionSpecificInstructions:
    "Dev Notes":
      - Populate relevant information, only what was pulled from actual artifacts from docs folder, relevant to this story
      - Do not invent information.
      - If known add Relevant Source Tree info that relates to this story.
      - If there were important notes from previous story that are relevant to this one, include them here.
      - Put enough information in this section so that the dev agent should NEVER need to read the architecture documents, these notes along with the tasks and subtasks must give the Dev Agent the complete context it needs to comprehend with the least amount of overhead the information to complete the story,  meeting all AC and completing all tasks+subtasks.
    Testing:
      - List Relevant Testing Standards from Architecture the Developer needs to conform to (test file location, test standards, etc) 
---

# Story 2.1: Create multiple transactions at once (bulk import)

## Status: ✅ COMPLETED

## Story

**As a** user of the YNAB MCP server,\
**I want** to create multiple transactions in a single operation,\
**so that** I can efficiently import bulk transactions and reduce the number of API calls needed

## Acceptance Criteria

1. <PERSON>l successfully creates multiple transactions from YNAB API for a specified budget
2. Tool accepts an array of transaction objects with required fields (account_id, date, amount, payee_name, category_id, memo)
3. Tool returns created transaction data in a clear, structured format with transaction IDs
4. Tool handles authentication errors gracefully with meaningful error messages
5. Tool follows the same patterns as existing tools (CreateTransactionTool, GetPayeesTool, etc.)
6. Tool includes proper logging for debugging and monitoring
7. Tool is properly integrated into the MCP server and available for use
8. Tool is tested against live YNAB development budget
9. Tool validates required transaction fields and provides helpful error messages
10. Tool handles partial failures gracefully (some transactions succeed, others fail)

## Tasks / Subtasks

- [ ] Task 1: Create CreateMultipleTransactionsTool.ts file (AC: 1, 2, 4, 5, 6, 9)
  - [ ] Create new file `src/tools/CreateMultipleTransactionsTool.ts`
  - [ ] Import required dependencies (MCPTool, logger, ynab API, zod)
  - [ ] Define tool schema with budgetId and transactions array parameters
  - [ ] Implement execute method to call YNAB transactions API with bulk data
  - [ ] Add proper error handling and logging
  - [ ] Return created transaction data in consistent format
- [ ] Task 2: Register tool in MCP server (AC: 7)
  - [ ] Tool auto-registers with MCP server (no manual registration needed)
  - [ ] Verify tool is available in MCP server
- [ ] Task 3: Create unit tests (AC: 4, 6, 9, 10)
  - [ ] Create test file `src/tools/__tests__/CreateMultipleTransactionsTool.test.ts`
  - [ ] Test successful bulk transaction creation
  - [ ] Test error handling scenarios (missing parameters, invalid data, partial failures)
  - [ ] Test logging functionality
- [x] Task 4: Integration testing with live YNAB API (AC: 1, 2, 3, 8)
  - [x] Test tool against live YNAB development budget
  - [x] Verify transaction data structure matches expectations
  - [x] Test error scenarios with invalid tokens and transaction data
  - [x] Document test results

## Dev Notes

### Previous Story Insights
- Epic 1 completed successfully with GetPayeesTool and GetSinglePayeeTool
- Existing tools follow consistent pattern: extend MCPTool, use ynab API, include proper error handling
- CreateTransactionTool already exists for single transactions - this builds on that pattern
- Error handling includes try-catch with logger.error and return error message
- Successful responses are logged with logger.info
- **Lesson learned**: Keep it simple, follow existing patterns, don't over-engineer
- **New insight**: Bulk operations require array validation and partial failure handling

### Data Models
- **Bulk Transaction Creation Request** [Source: node_modules/ynab/dist/models/PostTransactionsWrapper.d.ts]:
  ```typescript
  interface PostTransactionsWrapper {
    transaction?: NewTransaction;  // For single transaction
    transactions?: Array<NewTransaction>;  // For bulk transactions
  }
  ```

- **New Transaction Structure** [Source: node_modules/ynab/dist/models/NewTransaction.d.ts]:
  ```typescript
  interface NewTransaction {
    account_id: string;
    date: string;  // YYYY-MM-DD format
    amount: number;  // Amount in milliunits (cents * 10)
    payee_name?: string;
    payee_id?: string;
    category_id?: string;
    memo?: string;
    cleared?: boolean;
    approved?: boolean;
    flag_color?: string;
    import_id?: string;
  }
  ```

- **Bulk Transaction Response** [Source: node_modules/ynab/dist/models/SaveTransactionsResponse.d.ts]:
  ```typescript
  interface SaveTransactionsResponse {
    data: SaveTransactionsResponseData;
  }
  
  interface SaveTransactionsResponseData {
    transaction_ids: Array<string>;
    duplicate_import_ids: Array<string>;
    transactions: Array<TransactionDetail>;
  }
  ```

### API Specifications
- **YNAB Bulk Transaction Creation API Endpoint** [Source: node_modules/ynab/dist/apis/TransactionsApi.d.ts]:
  - Method: `api.transactions.createTransaction(budgetId, data)`
  - Returns: `Promise<SaveTransactionsResponse>`
  - Parameters: budgetId (required), data (PostTransactionsWrapper with transactions array)
  - Uses existing authentication token from environment
  - Supports both single and bulk transactions based on data structure

### File Locations
- **New Tool File**: `src/tools/CreateMultipleTransactionsTool.ts`
- **Test File**: `src/tools/__tests__/CreateMultipleTransactionsTool.test.ts`
- **Server Registration**: Auto-registration (no manual registration needed)
- **Follows existing pattern**: Same structure as `src/tools/CreateTransactionTool.ts` but for bulk operations

### Technical Constraints
- **Authentication**: Uses `process.env.YNAB_API_TOKEN` (same as existing tools)
- **Budget ID**: Required parameter (no environment variable fallback for bulk operations)
- **Transaction Array**: Required parameter - must be provided by user
- **Amount Format**: Must be in milliunits (cents * 10) as per YNAB API
- **Date Format**: Must be YYYY-MM-DD format
- **Error Handling**: Must handle missing API token, missing parameters, invalid transaction data, and partial failures
- **Logging**: Use `logger.info` for success, `logger.error` for failures
- **Return Format**: Return created transaction data with IDs, or error message string
- **Dependencies**: Uses existing `ynab` npm package and `mcp-framework`

### Testing Requirements
- **Unit Tests**: Use Vitest framework (same as existing tests)
- **Test Location**: `src/tools/__tests__/CreateMultipleTransactionsTool.test.ts`
- **Live Testing**: Test against YNAB development budget using existing API token
- **Error Scenarios**: Test missing token, missing parameters, invalid transaction data, partial failures
- **Success Scenarios**: Verify bulk transaction creation and returned transaction IDs

### Relevant Source Tree Info
- **Existing Tools Pattern**: `src/tools/CreateTransactionTool.ts` shows the exact pattern to follow
- **MCP Framework**: Uses `MCPTool` base class and `logger` from `mcp-framework`
- **YNAB API**: Uses `ynab.API` constructor with token, then `api.transactions.createTransaction()`
- **Parameter Validation**: Use Zod schema with required parameters (budgetId, transactions array)

### New Value Provided
- **Bulk transaction creation**: Creates multiple transactions in a single API call
- **Efficiency**: Reduces API calls for bulk imports
- **Transaction management**: Enables bulk transaction workflows
- **Error handling**: Provides partial failure handling for bulk operations
- **Data validation**: Ensures all required transaction fields are present

## Testing

### Testing Standards
- **Framework**: Vitest (as configured in package.json)
- **Test Location**: `src/tools/__tests__/CreateMultipleTransactionsTool.test.ts`
- **Coverage**: Unit tests for all code paths
- **Live Testing**: Must test against actual YNAB development budget
- **Error Testing**: Test all error scenarios (missing token, missing parameters, invalid transaction data, partial failures)

### Key Test Scenarios
1. **Successful Bulk Transaction Creation**: Verify multiple transactions are created correctly
2. **Missing API Token**: Should return meaningful error message
3. **Missing Budget ID**: Should return error message about required parameter
4. **Missing Transactions Array**: Should return error message about required parameter
5. **Invalid Transaction Data**: Should handle validation errors gracefully
6. **Partial Failures**: Should handle cases where some transactions succeed and others fail
7. **Network Errors**: Should handle API connection issues
8. **Logging Verification**: Ensure proper logging on success and failure
9. **Live Budget Testing**: Test against YNAB development budget with valid transaction data

## Change Log

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
| 2024-12-19 | 1.0 | Initial story creation | Bob (Scrum Master) |
| 2024-12-19 | 1.1 | Story completed successfully | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used: James (Dev Agent)

### Debug Log References
- Tool successfully auto-registered as `create_multiple_transactions`
- All unit tests passed (9/9)
- Build completed successfully with no TypeScript errors
- Schema validation issues resolved (camelCase vs snake_case)
- Amount sign handling clarified and documented

### Completion Notes List
- ✅ Created CreateMultipleTransactionsTool.ts following established patterns
- ✅ Implemented bulk transaction creation with proper error handling
- ✅ Added comprehensive parameter validation using Zod schema
- ✅ Created unit tests covering all code paths and error scenarios
- ✅ Tool auto-registers with MCP server (no manual registration needed)
- ✅ Integration testing completed by user via MCP client
- ✅ Fixed schema validation for camelCase input fields
- ✅ Clarified amount sign handling (user provides signed amounts)
- ✅ Updated tests to match camelCase schema

### File List
- `src/tools/CreateMultipleTransactionsTool.ts` - Main tool implementation
- `src/tools/__tests__/CreateMultipleTransactionsTool.test.ts` - Comprehensive unit tests

## QA Results
- ✅ Unit tests: 9/9 passed
- ✅ Build: Successful compilation
- ✅ Auto-registration: Tool loaded successfully as `create_multiple_transactions`
- ✅ Integration testing: Completed by user via MCP client
- ✅ Schema validation: Fixed camelCase input handling
- ✅ Amount handling: Clarified signed amount requirements 