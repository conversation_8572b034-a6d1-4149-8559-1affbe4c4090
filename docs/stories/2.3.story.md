---
defaultOutput: docs/stories/2.3.UpdateSingleTransaction.story.md
smAgent:
  editableSections: Status, Story, Acceptance Criteria, Tasks / Subtasks, Dev Notes, Testing, Change Log
  sectionSpecificInstructions:
    "Dev Notes":
      - Populate relevant information, only what was pulled from actual artifacts from docs folder, relevant to this story
      - Do not invent information.
      - If known add Relevant Source Tree info that relates to this story.
      - If there were important notes from previous story that are relevant to this one, include them here.
      - Put enough information in this section so that the dev agent should NEVER need to read the architecture documents, these notes along with the tasks and subtasks must give the Dev Agent the complete context it needs to comprehend with the least amount of overhead the information to complete the story,  meeting all AC and completing all tasks+subtasks.
    Testing:
      - List Relevant Testing Standards from Architecture the Developer needs to conform to (test file location, test standards, etc) 
---

# Story 2.3: Update a single existing transaction

## Status: Draft

## Story

**As a** user of the YNAB MCP server,\
**I want** to update a single existing transaction,\
**so that** I can modify individual transaction details like amount, payee, category, memo, etc.

## Acceptance Criteria

1. Tool successfully updates a single transaction from YNAB API for a specified budget
2. Tool accepts a transaction ID and optional update fields (amount, date, payee, category, memo, etc.)
3. Tool returns updated transaction data in a clear, structured format
4. Tool handles authentication errors gracefully with meaningful error messages
5. Tool follows the same patterns as existing tools (CreateTransactionTool, UpdateMultipleTransactionsTool, etc.)
6. Tool includes proper logging for debugging and monitoring
7. Tool is properly integrated into the MCP server and available for use
8. Tool is tested against live YNAB development budget
9. Tool validates required transaction fields and provides helpful error messages
10. Tool supports updating any combination of transaction fields
11. Tool handles transaction not found errors gracefully
12. Tool returns simple string responses (not complex objects)

## Tasks / Subtasks

- [ ] Task 1: Create UpdateSingleTransactionTool.ts file (AC: 1, 2, 4, 5, 6, 9, 10, 12)
  - [ ] Create new file `src/tools/UpdateSingleTransactionTool.ts`
  - [ ] Import required dependencies (MCPTool, logger, ynab API, zod)
  - [ ] Define tool schema with budgetId, transactionId, and optional update fields
  - [ ] Implement execute method to call YNAB transactions API with single update data
  - [ ] Add proper error handling and logging
  - [ ] Return updated transaction data in consistent format
  - [ ] Support updating any combination of transaction fields
- [ ] Task 2: Register tool in MCP server (AC: 7)
  - [ ] Tool auto-registers with MCP server (no manual registration needed)
  - [ ] Verify tool is available in MCP server
- [ ] Task 3: Create unit tests (AC: 4, 6, 9, 10, 11, 12)
  - [ ] Create test file `src/tools/__tests__/UpdateSingleTransactionTool.test.ts`
  - [ ] Test successful single transaction updates
  - [ ] Test error handling scenarios (missing parameters, invalid data, transaction not found)
  - [ ] Test logging functionality
  - [ ] Test updating various combinations of fields
- [ ] Task 4: Integration testing with live YNAB API (AC: 1, 2, 3, 8)
  - [ ] Test tool against live YNAB development budget
  - [ ] Verify transaction data structure matches expectations
  - [ ] Test error scenarios with invalid tokens and transaction data
  - [ ] Document test results

## Dev Notes

### Previous Story Insights
- Epic 1 completed successfully with GetPayeesTool and GetSinglePayeeTool
- Story 2.1 (CreateMultipleTransactionsTool) completed successfully
- Story 2.2 (UpdateMultipleTransactionsTool) completed successfully - this builds on that pattern
- Existing tools follow consistent pattern: extend MCPTool, use ynab API, include proper error handling
- Error handling includes try-catch with logger.error and return error message
- Successful responses are logged with logger.info
- **Lesson learned**: Keep it simple, follow existing patterns, don't over-engineer
- **New insight**: Single transaction updates use different API endpoint and data structure than bulk updates

### Data Models
- **Single Transaction Update Request** [Source: node_modules/ynab/dist/models/PutTransactionWrapper.d.ts]:
  ```typescript
  interface PutTransactionWrapper {
    transaction: ExistingTransaction;
  }
  ```

- **Transaction Update Structure** [Source: node_modules/ynab/dist/models/ExistingTransaction.d.ts]:
  ```typescript
  interface ExistingTransaction {
    // Updateable fields (all optional)
    account_id?: string;
    date?: string;  // YYYY-MM-DD format
    amount?: number;  // Amount in milliunits (cents * 10)
    payee_name?: string | null;
    payee_id?: string | null;
    category_id?: string | null;
    memo?: string | null;
    cleared?: "cleared" | "uncleared" | "reconciled";
    approved?: boolean;
    flag_color?: string | null;
    subtransactions?: Array<SaveSubTransaction>;
  }
  ```

- **Single Transaction Update Response** [Source: node_modules/ynab/dist/models/TransactionResponse.d.ts]:
  ```typescript
  interface TransactionResponse {
    data: TransactionResponseData;
  }
  
  interface TransactionResponseData {
    transaction: TransactionDetail;
  }
  ```

### API Specifications
- **YNAB Single Transaction Update API Endpoint** [Source: node_modules/ynab/dist/apis/TransactionsApi.d.ts]:
  - Method: `api.transactions.updateTransaction(budgetId, transactionId, data)`
  - Returns: `Promise<TransactionResponse>`
  - Parameters: budgetId (required), transactionId (required), data (PutTransactionWrapper with single transaction)
  - Uses existing authentication token from environment
  - Updates a single transaction by its ID

### File Locations
- **New Tool File**: `src/tools/UpdateSingleTransactionTool.ts`
- **Test File**: `src/tools/__tests__/UpdateSingleTransactionTool.test.ts`
- **Server Registration**: Auto-registration (no manual registration needed)
- **Follows existing pattern**: Same structure as `src/tools/UpdateMultipleTransactionsTool.ts` but for single updates

### Technical Constraints
- **Authentication**: Uses `process.env.YNAB_API_TOKEN` (same as existing tools)
- **Budget ID**: Required parameter (no environment variable fallback)
- **Transaction ID**: Required parameter - must be provided by user
- **Update Fields**: All optional - only provided fields will be updated
- **Amount Format**: Must be in milliunits (cents * 10) as per YNAB API
- **Date Format**: Must be YYYY-MM-DD format
- **Error Handling**: Must handle missing API token, missing parameters, invalid transaction data, and transaction not found
- **Logging**: Use `logger.info` for success, `logger.error` for failures
- **Return Format**: Return updated transaction data, or error message string
- **Dependencies**: Uses existing `ynab` npm package and `mcp-framework`

### Testing Requirements
- **Unit Tests**: Use Vitest framework (same as existing tests)
- **Test Location**: `src/tools/__tests__/UpdateSingleTransactionTool.test.ts`
- **Live Testing**: Test against YNAB development budget using existing API token
- **Error Scenarios**: Test missing token, missing parameters, invalid transaction data, transaction not found
- **Success Scenarios**: Verify single transaction updates and returned transaction data
- **Field Update Testing**: Test updating various combinations of transaction fields

### Relevant Source Tree Info
- **Existing Tools Pattern**: `src/tools/UpdateMultipleTransactionsTool.ts` shows the exact pattern to follow
- **MCP Framework**: Uses `MCPTool` base class and `logger` from `mcp-framework`
- **YNAB API**: Uses `ynab.API` constructor with token, then `api.transactions.updateTransaction()`
- **Parameter Validation**: Use Zod schema with required parameters (budgetId, transactionId) and optional update fields
- **Key Difference**: Uses `PutTransactionWrapper` instead of `PatchTransactionsWrapper`

### New Value Provided
- **Single transaction updates**: Updates individual transactions efficiently
- **Precision**: Allows targeted updates to specific transaction fields
- **Transaction management**: Enables individual transaction modification workflows
- **Error handling**: Provides specific error handling for single transaction operations
- **Data validation**: Ensures proper transaction identification and field validation
- **Flexibility**: Supports updating any combination of transaction fields

## Testing

### Testing Standards
- **Framework**: Vitest (as configured in package.json)
- **Test Location**: `src/tools/__tests__/UpdateSingleTransactionTool.test.ts`
- **Coverage**: Unit tests for all code paths
- **Live Testing**: Must test against actual YNAB development budget
- **Error Testing**: Test all error scenarios (missing token, missing parameters, invalid transaction data, transaction not found)

### Key Test Scenarios
1. **Successful Single Transaction Updates**: Verify individual transaction is updated correctly
2. **Missing API Token**: Should return meaningful error message
3. **Missing Budget ID**: Should return error message about required parameter
4. **Missing Transaction ID**: Should return error message about required parameter
5. **Invalid Transaction Data**: Should handle validation errors gracefully
6. **Transaction Not Found**: Should handle cases where transaction ID doesn't exist
7. **Network Errors**: Should handle API connection issues
8. **Logging Verification**: Ensure proper logging on success and failure
9. **Live Budget Testing**: Test against YNAB development budget with valid transaction data
10. **Field Update Testing**: Test updating various combinations of transaction fields
11. **Partial Field Updates**: Test updating only specific fields while leaving others unchanged

## Change Log

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
| 2024-12-19 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used: 

### Debug Log References

### Completion Notes List

### File List

## QA Results 