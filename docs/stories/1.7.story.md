---
defaultOutput: docs/stories/1.7.GetSinglePayeeDetails.story.md
smAgent:
  editableSections: Status, Story, Acceptance Criteria, Tasks / Subtasks, Dev Notes, Testing, Change Log
  sectionSpecificInstructions:
    "Dev Notes":
      - Populate relevant information, only what was pulled from actual artifacts from docs folder, relevant to this story
      - Do not invent information.
      - If known add Relevant Source Tree info that relates to this story.
      - If there were important notes from previous story that are relevant to this one, include them here.
      - Put enough information in this section so that the dev agent should NEVER need to read the architecture documents, these notes along with the tasks and subtasks must give the Dev Agent the complete context it needs to comprehend with the least amount of overhead the information to complete the story,  meeting all AC and completing all tasks+subtasks.
    Testing:
      - List Relevant Testing Standards from Architecture the Developer needs to conform to (test file location, test standards, etc) 
---

# Story 1.7: Get the details of a single payee

## Status: Done

## Story

**As a** user of the YNAB MCP server,\
**I want** to retrieve detailed information about a specific payee,\
**so that** I can get complete payee information for transaction management and payee analysis

## Acceptance Criteria

1. <PERSON>l successfully retrieves single payee details from YNAB API for a specified budget and payee ID
2. Tool returns payee data in a clear, structured format (id, name, transfer_account_id, deleted status)
3. Tool handles authentication errors gracefully with meaningful error messages
4. Tool follows the same patterns as existing tools (GetPayeesTool, BudgetSummaryTool, etc.)
5. Tool includes proper logging for debugging and monitoring
6. Tool is properly integrated into the MCP server and available for use
7. Tool is tested against live YNAB development budget
8. Tool validates required parameters (budgetId, payeeId) and provides helpful error messages

## Tasks / Subtasks

- [x] Task 1: Create GetSinglePayeeTool.ts file (AC: 1, 2, 4, 6, 8)
  - [x] Create new file `src/tools/GetSinglePayeeTool.ts`
  - [x] Import required dependencies (MCPTool, logger, ynab API, zod)
  - [x] Define tool schema with budgetId and payeeId parameters (both required)
  - [x] Implement execute method to call YNAB payee API
  - [x] Add proper error handling and logging
  - [x] Return payee data in consistent format
- [x] Task 2: Register tool in MCP server (AC: 6)
  - [x] Tool auto-registers with MCP server (no manual registration needed)
  - [x] Verify tool is available in MCP server
- [x] Task 3: Create unit tests (AC: 3, 5, 8)
  - [x] Create test file `src/tools/__tests__/GetSinglePayeeTool.test.ts`
  - [x] Test successful single payee retrieval
  - [x] Test error handling scenarios (missing parameters, invalid IDs)
  - [x] Test logging functionality
- [x] Task 4: Integration testing with live YNAB API (AC: 1, 2, 3, 7)
  - [x] Test tool against live YNAB development budget
  - [x] Verify payee data structure matches expectations
  - [x] Test error scenarios with invalid tokens and payee IDs
  - [x] Document test results

## Dev Notes

### Previous Story Insights
- Story 1.6 (GetPayeesTool) successfully implemented and working with live YNAB API
- Existing tools follow consistent pattern: extend MCPTool, use ynab API, include proper error handling
- Tools use schema with required parameters when needed (unlike optional budgetId in GetPayeesTool)
- Error handling includes try-catch with logger.error and return error message
- Successful responses are logged with logger.info
- **Lesson learned**: Keep it simple, follow existing patterns, don't over-engineer
- **New insight**: Single entity tools require both budgetId and entityId parameters

### Data Models
- **Single Payee API Response Structure** [Source: node_modules/ynab/dist/models/PayeeResponse.d.ts]:
  ```typescript
  interface PayeeResponse {
    data: PayeeResponseData;
  }
  
  interface PayeeResponseData {
    payee: Payee;
  }
  
  interface Payee {
    id: string;
    name: string;
    transfer_account_id?: string | null;
    deleted: boolean;
  }
  ```

### API Specifications
- **YNAB Single Payee API Endpoint** [Source: node_modules/ynab/dist/apis/PayeesApi.d.ts]:
  - Method: `api.payees.getPayeeById(budgetId, payeeId)`
  - Returns: `Promise<PayeeResponse>`
  - Parameters: budgetId (required), payeeId (required)
  - Uses existing authentication token from environment

### File Locations
- **New Tool File**: `src/tools/GetSinglePayeeTool.ts`
- **Test File**: `src/tools/__tests__/GetSinglePayeeTool.test.ts`
- **Server Registration**: Auto-registration (no manual registration needed)
- **Follows existing pattern**: Same structure as `src/tools/GetPayeesTool.ts` but with required parameters

### Technical Constraints
- **Authentication**: Uses `process.env.YNAB_API_TOKEN` (same as existing tools)
- **Budget ID**: Required parameter (no environment variable fallback for single entity tools)
- **Payee ID**: Required parameter - must be provided by user
- **Error Handling**: Must handle missing API token, missing parameters, and API errors gracefully
- **Logging**: Use `logger.info` for success, `logger.error` for failures
- **Return Format**: Return single payee object, or error message string
- **Dependencies**: Uses existing `ynab` npm package and `mcp-framework`

### Testing Requirements
- **Unit Tests**: Use Vitest framework (same as existing tests)
- **Test Location**: `src/tools/__tests__/GetSinglePayeeTool.test.ts`
- **Live Testing**: Test against YNAB development budget using existing API token
- **Error Scenarios**: Test missing token, missing parameters, invalid payee ID, network errors
- **Success Scenarios**: Verify single payee data is returned correctly

### Relevant Source Tree Info
- **Existing Tools Pattern**: `src/tools/GetPayeesTool.ts` shows the exact pattern to follow
- **MCP Framework**: Uses `MCPTool` base class and `logger` from `mcp-framework`
- **YNAB API**: Uses `ynab.API` constructor with token, then `api.payees.getPayeeById()`
- **Parameter Validation**: Use Zod schema with required parameters (budgetId, payeeId)

### New Value Provided
- **Single payee details**: Provides detailed information about a specific payee
- **Transaction management**: Enables payee-specific operations and analysis
- **Data validation**: Ensures payee exists before operations
- **Error handling**: Provides specific error messages for invalid payee IDs

## Testing

### Testing Standards
- **Framework**: Vitest (as configured in package.json)
- **Test Location**: `src/tools/__tests__/GetSinglePayeeTool.test.ts`
- **Coverage**: Unit tests for all code paths
- **Live Testing**: Must test against actual YNAB development budget
- **Error Testing**: Test all error scenarios (missing token, missing parameters, invalid payee ID, API errors)

### Key Test Scenarios
1. **Successful Single Payee Retrieval**: Verify single payee data is returned correctly
2. **Missing API Token**: Should return meaningful error message
3. **Missing Budget ID**: Should return error message about required parameter
4. **Missing Payee ID**: Should return error message about required parameter
5. **Invalid Payee ID**: Should handle API errors gracefully
6. **Network Errors**: Should handle API connection issues
7. **Logging Verification**: Ensure proper logging on success and failure
8. **Live Budget Testing**: Test against YNAB development budget with valid payee ID

## Change Log

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
| 2024-12-19 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used: Claude Sonnet 4 (James - Dev Agent)

### Debug Log References
- Build successful: TypeScript compilation and MCP build completed without errors
- Unit tests: All 8 test cases passed (error handling, success scenarios, parameter validation)
- Live API testing: Confirmed by user that tool successfully retrieves single payee details from YNAB

### Completion Notes List
- Created GetSinglePayeeTool.ts following established pattern from GetPayeesTool.ts
- Implemented required budgetId and payeeId parameters (no environment variable fallback for single entity tools)
- Added comprehensive error handling for missing API token, missing parameters, and API errors
- Included proper logging for debugging and monitoring
- Created unit tests covering all code paths including success, error, and edge cases
- Tool auto-registers with MCP server (no manual registration needed)
- All tests pass successfully (8 test scenarios)

### File List
- Created: `src/tools/GetSinglePayeeTool.ts`
- Created: `src/tools/__tests__/GetSinglePayeeTool.test.ts`
- Modified: `docs/stories/1.7.story.md` (updated task completion and Dev Agent Record)

## QA Results 