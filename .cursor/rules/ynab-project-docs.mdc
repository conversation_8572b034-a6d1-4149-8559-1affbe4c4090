---
description: YNAB MCP Server Project Documentation and Pattern References
globs: ["**/*.ts", "**/*.js", "**/*.md"]
alwaysApply: true
---

# YNAB MCP Server Project Documentation Rules

## 📋 **CRITICAL: Always Reference These Documents**

### Project Backlog & Stories
- **Main Backlog**: [Storries](mdc:docs/Storries) - Complete project roadmap with epics and stories
- **Current Stories**: [docs/stories/](mdc:docs/stories/) - Individual story files with requirements
- **Lessons Learned**: [lessons-learned.md](mdc:docs/lessons-learned.md) - Project insights and patterns

### Architecture & Patterns
- **Existing Tools Pattern**: [ListBudgetsTool.ts](mdc:src/tools/ListBudgetsTool.ts) - Base pattern for all tools
- **Complex Tool Pattern**: [BudgetSummaryTool.ts](mdc:src/tools/BudgetSummaryTool.ts) - Pattern for tools with parameters
- **YNAB API Types**: [index.d.ts](mdc:node_modules/ynab/dist/index.d.ts) - Always check type definitions here

### Project Configuration
- **Package.json**: [package.json](mdc:package.json) - Dependencies, scripts, and project metadata
- **README**: [README.md](mdc:README.md) - Current state, available tools, and next priorities

## 🎯 **Development Patterns (MUST FOLLOW)**

### Tool Creation Pattern
1. **Extend MCPTool**: `class NewTool extends MCPTool<InputInterface>`
2. **Schema Definition**: Use Zod for parameter validation
3. **Error Handling**: Try-catch with logger.error and meaningful error messages
4. **Logging**: Use `logger.info` for success, `logger.error` for failures
5. **Return Format**: Return structured data or error message string
6. **Testing**: Create comprehensive unit tests in `src/tools/__tests__/`

### File Structure
- **Tools**: `src/tools/ToolName.ts`
- **Tests**: `src/tools/__tests__/ToolName.test.ts`
- **Stories**: `docs/stories/X.Y.story.md`
- **Auto-registration**: Tools auto-discover, no manual registration needed

### YNAB API Integration
- **Authentication**: Use `process.env.YNAB_API_TOKEN`
- **Budget ID**: Use `process.env.YNAB_BUDGET_ID` or accept as parameter
- **API Calls**: Use `this.api.endpoint.method()` pattern
- **Type Safety**: Always import types from `node_modules/ynab/dist/models/`

## 🚫 **Common Mistakes to Avoid**

1. **Don't create authentication tools** - Already proven by existing tools
2. **Don't duplicate data** - Check if BudgetSummaryTool already provides the data
3. **Don't over-engineer** - Follow existing simple patterns
4. **Don't skip testing** - All tools must have comprehensive unit tests
5. **Don't forget error handling** - Always handle missing tokens and API errors

## 📚 **Reference Documents by Epic**

### Epic 1: Payee Data Read-Access
- ✅ Story 1.6: GetPayeesTool.ts (COMPLETED)
- [ ] Story 1.7: Single payee details
- [ ] Story 1.8: Payee locations list
- [ ] Story 1.9: Single payee location details

### Epic 2: Transaction Management  
- [ ] Stories 2.1-2.9: Bulk operations, updates, scheduled transactions

### Epic 3: Budget & Month Management
- [ ] Stories 3.1-3.3: Category updates, month details

## 🔍 **Before Creating Any New Tool**

1. **Check Storries**: Ensure the tool is in the backlog
2. **Check existing tools**: Avoid duplicating functionality
3. **Check YNAB API types**: Use correct interfaces and methods
4. **Follow established patterns**: Copy from existing working tools
5. **Create comprehensive tests**: Cover all code paths
6. **Test against live API**: Use YNAB development budget

## 📝 **Story Development Process**

1. **Read story file**: `docs/stories/X.Y.story.md`
2. **Check Dev Notes**: Contains all necessary technical context
3. **Follow Tasks/Subtasks**: Complete all items sequentially
4. **Update Dev Agent Record**: Document completion and file changes
5. **Mark Status**: Update to "Ready for Review" when complete

**Remember**: The story files contain ALL the information needed - don't load other docs unless explicitly directed!
 