---
alwaysApply: true
---


"Be more proactive, just fix things unless I say stop."
"Always explain what you're doing before you do it."
"Give me the shortest possible answer."
"Just run commands unless I have said otherwise. or it would be a distructive command"
"Summarize all options, then recommend the best one for me."
"When dealing with running things locally always make sure that the port being used is clear before using it."
- "On port conflict, always auto-kill the process and restart the service without prompting."
- For intefrated testing prompt the user to run the tests and confirm the results before proceeding."
- "Do NOT test against live APIs - before marking a story as complete, the user needs to test in Augment Chat with the MCP. That will be our live API test."
- "Complete all unit tests and code implementation, then wait for user to test in Augment Chat before marking story complete."
- "After each successful story implementation (as indicated by the user), push changes to GitHub."