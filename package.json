{"name": "ynab-mcp-server", "version": "0.1.2", "description": "ynab-mcp-server MCP server", "author": "<PERSON> <<EMAIL>>", "type": "module", "bin": {"ynab-mcp-server": "./dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && mcp-build && chmod +x dist/index.js", "prepare": "npm run build", "watch": "tsc --watch", "start": "node dist/index.js", "debug": "npx @modelcontextprotocol/inspector dist/index.js", "test": "vitest", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage"}, "dependencies": {"@types/axios": "^0.14.4", "axios": "^1.8.4", "mcp-framework": "^0.1.29", "ynab": "^2.9.0"}, "devDependencies": {"@types/node": "^20.11.24", "@vitest/coverage-v8": "^1.4.0", "typescript": "^5.3.3", "vitest": "^1.4.0"}}